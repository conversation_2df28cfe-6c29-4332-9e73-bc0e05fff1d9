package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 状态变更请求DTO
 *
 * 用于单个增值交付单状态变更的请求参数封装
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("状态变更请求DTO")
public class StatusChangeRequestDTO extends BaseStatusChangeRequestDTO {

    /**
     * 交付单编号
     */
    @NotBlank(message = "交付单编号不能为空")
    @Size(max = 50, message = "交付单编号长度不能超过50个字符")
    @ApiModelProperty(value = "交付单编号", required = true, example = "VAD2508051430001A1C")
    private String deliveryOrderNo;
}
