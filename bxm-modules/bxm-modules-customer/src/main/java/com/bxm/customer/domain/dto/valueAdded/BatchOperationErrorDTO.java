package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量操作异常数据DTO
 *
 * 用于记录批量操作过程中失败的交付单信息
 * 包含完整的错误上下文，便于问题排查和数据导出
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量操作异常数据DTO")
public class BatchOperationErrorDTO {

    /**
     * 交付单编号
     */
    @Excel(name = "交付单编号")
    @ApiModelProperty(value = "交付单编号")
    private String deliveryOrderNo;

    /**
     * 统一社会信用代码
     */
    @Excel(name = "统一社会信用代码")
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /**
     * 集团ID（顶级业务部门ID）
     */
    @Excel(name = "集团ID")
    @ApiModelProperty(value = "集团ID")
    private Long topDeptId;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 交付单标题
     */
    @Excel(name = "交付单标题")
    @ApiModelProperty(value = "交付单标题")
    private String title;

    /**
     * 当前状态
     */
    @ApiModelProperty(value = "当前状态")
    private String currentStatus;

    /**
     * 当前状态
     */
    @Excel(name = "当前状态")
    @ApiModelProperty(value = "当前状态")
    private String currentStatusName;

    /**
     * 目标状态
     */
    @ApiModelProperty(value = "目标状态")
    private String targetStatus;

    @Excel(name = "目标状态")
    @ApiModelProperty(value = "目标状态")
    private String targetStatusName;

    /**
     * 操作类型
     */
    @Excel(name = "操作类型")
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息")
    @ApiModelProperty(value = "错误信息")
    private String errorInfo;



    /**
     * 创建错误数据的静态方法
     */
    public static BatchOperationErrorDTO createError(String deliveryOrderNo, String creditCode,
                                                   Long topDeptId, String customerName, String title,
                                                   String currentStatus, String targetStatus,
                                                  String errorInfo
                                                   ) {
        // 安全获取状态描述，避免空指针异常
        String currentStatusName = currentStatus != null ?
            (ValueAddedDeliveryOrderStatus.getByCode(currentStatus) != null ?
                ValueAddedDeliveryOrderStatus.getByCode(currentStatus).getDescription() : currentStatus) : "未知";
        String targetStatusName = targetStatus != null ?
            (ValueAddedDeliveryOrderStatus.getByCode(targetStatus) != null ?
                ValueAddedDeliveryOrderStatus.getByCode(targetStatus).getDescription() : targetStatus) : "未知";

        return BatchOperationErrorDTO.builder()
                .deliveryOrderNo(deliveryOrderNo)
                .creditCode(creditCode)
                .topDeptId(topDeptId)
                .customerName(customerName)
                .title(title)
                .currentStatus(currentStatus)
                .currentStatusName(currentStatusName)
                .targetStatus(targetStatus)
                .targetStatusName(targetStatusName)
                .errorInfo(errorInfo)
                .build();
    }
}
