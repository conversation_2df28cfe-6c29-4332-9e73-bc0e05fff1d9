package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 补账临时的 材料交接票据对象 c_customer_service_repair_account_temp_tax_instrument
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ApiModel("补账临时的 材料交接票据对象")
@Accessors(chain = true)
@TableName("c_customer_service_repair_account_temp_tax_instrument")
public class CustomerServiceRepairAccountTempTaxInstrument extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 关联id */
    @Excel(name = "关联id")
    @TableField("customer_service_repair_account_id")
    @ApiModelProperty(value = "关联id")
    private Long customerServiceRepairAccountId;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 发票业务：1-其他票据/2-税号发票 */
    @Excel(name = "发票业务：1-其他票据/2-税号发票")
    @TableField("biz_type")
    @ApiModelProperty(value = "发票业务：1-其他票据/2-税号发票")
    private Integer bizType;

    /** 名称 */
    @Excel(name = "名称")
    @TableField("name")
    @ApiModelProperty(value = "名称")
    private String name;

    /** 第几行，主要name可为空，没啥做标记的，有些是批量操作，所以这边加了个rowNum */
    @Excel(name = "第几行，主要name可为空，没啥做标记的，有些是批量操作，所以这边加了个rowNum")
    @TableField("row_num")
    @ApiModelProperty(value = "第几行，主要name可为空，没啥做标记的，有些是批量操作，所以这边加了个rowNum")
    private Integer rowNum;

    /** 纸质数量 */
    @Excel(name = "纸质数量")
    @TableField("paper_count")
    @ApiModelProperty(value = "纸质数量")
    private Integer paperCount;

    /** 税号说明 */
    @Excel(name = "税号说明")
    @TableField("remark")
    @ApiModelProperty(value = "税号说明")
    private String remark;

}
