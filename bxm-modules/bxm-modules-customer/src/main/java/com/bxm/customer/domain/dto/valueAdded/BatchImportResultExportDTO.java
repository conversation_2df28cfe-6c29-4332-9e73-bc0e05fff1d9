package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量导入结果导出DTO
 *
 * 用于导出批量导入的成功和错误记录到Excel
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchImportResultExportDTO {

    /**
     * 交付单编号
     */
    @Excel(name = "交付单编号", width = 20)
    private String deliveryOrderNo;

    /**
     * 统一社会信用代码
     */
    @Excel(name = "统一社会信用代码", width = 25)
    private String creditCode;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称", width = 30)
    private String customerName;

    /**
     * 处理状态
     */
    @Excel(name = "处理状态", width = 15)
    private String processStatus;

    /**
     * 错误类型
     */
    @Excel(name = "错误类型", width = 20)
    private String errorType;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息", width = 50)
    private String errorMessage;

    /**
     * 操作人
     */
    @Excel(name = "操作人", width = 15)
    private String operator;

    /**
     * 创建成功记录
     */
    public static BatchImportResultExportDTO createSuccess(String deliveryOrderNo, String creditCode, String customerName, String operator) {
        return BatchImportResultExportDTO.builder()
                .deliveryOrderNo(deliveryOrderNo)
                .creditCode(creditCode)
                .customerName(customerName)
                .processStatus("成功")
                .errorType("")
                .errorMessage("")
                .operator(operator)
                .build();
    }

    /**
     * 创建错误记录
     */
    public static BatchImportResultExportDTO createError(String deliveryOrderNo, String creditCode, String customerName,
                                                        String errorType, String errorMessage) {
        return BatchImportResultExportDTO.builder()
                .deliveryOrderNo(deliveryOrderNo)
                .creditCode(creditCode)
                .customerName(customerName)
                .processStatus("失败")
                .errorType(errorType != null ? errorType : "")
                .errorMessage(errorMessage != null ? errorMessage : "")
                .build();
    }
}
