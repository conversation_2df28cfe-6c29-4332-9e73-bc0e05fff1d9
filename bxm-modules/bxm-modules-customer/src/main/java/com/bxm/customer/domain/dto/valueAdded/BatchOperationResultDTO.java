package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量操作结果DTO
 *
 * 用于返回批量操作的执行结果
 * 包含成功数量、失败数量、错误数据批次号等信息
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量操作结果DTO")
public class BatchOperationResultDTO {

    /**
     * 批次号（用于导出异常数据）
     */
    @ApiModelProperty(value = "批次号，用于导出异常数据")
    private String batchNo;

    /**
     * 操作类型描述
     */
    @ApiModelProperty(value = "操作类型描述")
    private String operationDescription;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    /**
     * 成功数量
     */
    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量")
    private Integer errorCount;

    /**
     * 成功的交付单编号列表
     */
    @ApiModelProperty(value = "成功的交付单编号列表")
    private List<String> successOrderNos;

    /**
     * 失败的交付单编号列表
     */
    @ApiModelProperty(value = "失败的交付单编号列表")
    private List<String> errorOrderNos;

    /**
     * 操作开始时间
     */
    @ApiModelProperty(value = "操作开始时间")
    private String startTime;

    /**
     * 操作结束时间
     */
    @ApiModelProperty(value = "操作结束时间")
    private String endTime;


    /**
     * 是否有异常数据
     */
    @ApiModelProperty(value = "是否有异常数据")
    private Boolean hasErrors;

    /**
     * 操作结果摘要
     */
    @ApiModelProperty(value = "操作结果摘要")
    private String summary;
}
