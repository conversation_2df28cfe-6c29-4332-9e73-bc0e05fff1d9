package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.uuid.IdUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.config.BatchOperationConfig;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.valueAdded.*;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.enums.ValueAddedOperTypeEnum;
import com.bxm.customer.service.IBatchValueAddedOperationService;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.ValueAddedDeliveryOrderStateMachineManager;
import com.bxm.customer.service.strategy.ImportOperationStrategy;
import com.bxm.customer.service.strategy.ImportOperationStrategyFactory;
import com.bxm.customer.utils.FileExtractionUtils;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.Date;


/**
 * 批量操作服务实现类
 * <p>
 * 提供增值交付单的各种批量操作功能实现
 * 包括批量确认、提交、关闭、处理异常、驳回、退回等操作
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Slf4j
@Service
public class ValueAddedBatchOperationServiceImpl implements IBatchValueAddedOperationService {

    @Autowired
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BatchOperationConfig batchOperationConfig;

    @Autowired
    private ImportOperationStrategyFactory importStrategyFactory;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private ValueAddedDeliveryOrderStateMachineManager stateMachineManager;

    @Override
    public BatchOperationResultDTO executeBatchOperation(BatchOperationRequestDTO request) {
        // 验证请求参数
        validateBatchOperationRequest(request);

        // 批量查询交付单
        List<ValueAddedDeliveryOrder> orders = batchQueryOrders(request.getDeliveryOrderNos());

        // 执行批量操作
        BatchOperationResult result = processBatchOperation(orders, request);

        // 缓存异常数据
        String batchNo = null;
        if (!result.getErrors().isEmpty()) {
            batchNo = IdUtils.fastSimpleUUID();
            cacheErrorData(batchNo, result.getErrors());
        }


        // 构建返回结果
        BatchOperationResultDTO resultDTO = BatchOperationResultDTO.builder()
                .batchNo(batchNo)
                .totalCount(orders.size())
                .successCount(result.getSuccessOrderNos().size())
                .errorCount(result.getErrors().size())
                .successOrderNos(result.getSuccessOrderNos())
                .errorOrderNos(result.getErrors().stream()
                        .map(BatchOperationErrorDTO::getDeliveryOrderNo)
                        .collect(Collectors.toList()))
                .hasErrors(!result.getErrors().isEmpty())
                .build();
        return resultDTO;
    }

    @Override
    public List<BatchOperationErrorDTO> getBatchOperationErrors(String batchNo) {
        if (batchNo == null || batchNo.trim().isEmpty()) {
            throw new IllegalArgumentException("批次号不能为空");
        }

        List<BatchOperationErrorDTO> errorList = redisService.getLargeCacheList(
                CacheConstants.BATCH_OPERATION_ERROR_RECORD + batchNo, batchOperationConfig.getRedisBatchSize());

        return ObjectUtils.isEmpty(errorList) ? new ArrayList<>() : errorList;
    }

    @Override
    public void validateBatchOperationRequest(BatchOperationRequestDTO request) {
        // 验证批量退回操作的前置状态
        request.validateBatchReturnSourceStatus();

        // 验证交付单数量限制
        if (request.getOrderCount() > batchOperationConfig.getMaxBatchSize()) {
            throw new IllegalArgumentException("单次批量操作最多支持" + batchOperationConfig.getMaxBatchSize() + "条记录");
        }

        // 验证交付单编号格式
        for (String orderNo : request.getDeliveryOrderNos()) {
            if (orderNo == null || orderNo.trim().isEmpty()) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }
        }
    }

    @Override
    public String determineBatchReturnTargetStatus(String sourceStatus) {
        if (sourceStatus == null || sourceStatus.trim().isEmpty()) {
            throw new IllegalArgumentException("前置状态不能为空");
        }

        ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(sourceStatus);
        if (currentStatus == null) {
            throw new IllegalArgumentException("无效的前置状态: " + sourceStatus);
        }

        // 定义退回规则映射
        Map<ValueAddedDeliveryOrderStatus, ValueAddedDeliveryOrderStatus> returnMapping = new HashMap<>();
        returnMapping.put(ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED, ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION);
        returnMapping.put(ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION, ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION);
        returnMapping.put(ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION, ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY);
        returnMapping.put(ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY, ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT);

        ValueAddedDeliveryOrderStatus targetStatus = returnMapping.get(currentStatus);
        if (targetStatus == null) {
            throw new IllegalArgumentException("状态 " + currentStatus.getDescription() + " 不支持退回操作");
        }

        return targetStatus.getCode();
    }

    /**
     * 批量查询交付单
     */
    private List<ValueAddedDeliveryOrder> batchQueryOrders(List<String> deliveryOrderNos) {
        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNos);
        queryWrapper.eq(ValueAddedDeliveryOrder::getIsDel, false);

        List<ValueAddedDeliveryOrder> orders = valueAddedDeliveryOrderService.list(queryWrapper);

        // 检查是否有交付单不存在
        Set<String> foundOrderNos = orders.stream()
                .map(ValueAddedDeliveryOrder::getDeliveryOrderNo)
                .collect(Collectors.toSet());

        List<String> notFoundOrderNos = deliveryOrderNos.stream()
                .filter(orderNo -> !foundOrderNos.contains(orderNo))
                .collect(Collectors.toList());

        if (!notFoundOrderNos.isEmpty()) {
            throw new IllegalArgumentException("以下交付单不存在: " + String.join(", ", notFoundOrderNos));
        }

        return orders;
    }

    /**
     * 处理批量操作
     */
    private BatchOperationResult processBatchOperation(List<ValueAddedDeliveryOrder> orders,
                                                       BatchOperationRequestDTO request) {
        List<BatchOperationErrorDTO> errors = new ArrayList<>();
        List<String> successOrderNos = new ArrayList<>();

        for (ValueAddedDeliveryOrder order : orders) {
            try {
                // 1. 验证前置状态（如果指定了sourceStatus）
                if (!StringUtils.isEmpty(request.getSourceStatus()) && !Objects.equals(order.getStatus(), request.getSourceStatus())) {
                    String currentStatusDesc = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus()) != null ?
                        ValueAddedDeliveryOrderStatus.getByCode(order.getStatus()).getDescription() : order.getStatus();
                    String expectedStatusDesc = ValueAddedDeliveryOrderStatus.getByCode(request.getSourceStatus()) != null ?
                        ValueAddedDeliveryOrderStatus.getByCode(request.getSourceStatus()).getDescription() : request.getSourceStatus();
                    errors.add(buildErrorData(order, request, "前置状态不符，当前状态: " + currentStatusDesc + "，期望状态: " + expectedStatusDesc));
                    continue;
                }

                // 2. 解析当前状态
                ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
                if (currentStatus == null) {
                    errors.add(buildErrorData(order, request, "当前状态无效: " + order.getStatus()));
                    continue;
                }

                // 3. 解析目标状态
                ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());
                if (targetStatus == null) {
                    errors.add(buildErrorData(order, request, "目标状态无效: " + request.getTargetStatus()));
                    continue;
                }

                // 4. 构建状态变更请求
                StatusChangeRequestDTO changeRequest = buildStatusChangeRequest(order, request.getTargetStatus(), request);

                // 5. 直接调用 changeStatus 方法来完成完整的状态变更流程
                try {
                    valueAddedDeliveryOrderService.changeStatus(changeRequest);
                } catch (Exception e) {
                    // 如果 changeStatus 失败，记录错误但不中断批量操作
                    errors.add(buildErrorData(order, request, "状态变更失败: " + e.getMessage()));
                    continue;
                }

                successOrderNos.add(order.getDeliveryOrderNo());

            } catch (Exception e) {
                log.warn("Batch operation failed for order: {}, error: {}", order.getDeliveryOrderNo(), e.getMessage());

                errors.add(buildErrorData(order, request, e.getMessage()));
            }
        }

        return new BatchOperationResult(successOrderNos, errors);
    }



    /**
     * 构建状态变更请求（简化版本，直接使用BatchOperationRequestDTO中的字段）
     */
    private StatusChangeRequestDTO buildStatusChangeRequest(ValueAddedDeliveryOrder order,
                                                            String targetStatus,
                                                            BatchOperationRequestDTO request) {
        StatusChangeRequestDTO changeRequest = new StatusChangeRequestDTO();

        // 设置单个交付单特有字段
        changeRequest.setDeliveryOrderNo(order.getDeliveryOrderNo());

        // 设置从基类继承的字段
        changeRequest.setTargetStatus(targetStatus);
        changeRequest.setRemark(request.getRemark());
        changeRequest.setCreditCode(order.getCreditCode());
        changeRequest.setTotalWithholdingAmount(order.getTotalWithholdingAmount());
        changeRequest.setOperTypeName(request.getOperTypeName());
        changeRequest.setDeptId(request.getDeptId());

        return changeRequest;
    }

    /**
     * 构建错误数据（使用状态描述而不是状态代码）
     */
    private BatchOperationErrorDTO buildErrorData(ValueAddedDeliveryOrder order,
                                                  BatchOperationRequestDTO request,
                                                  String errorMessage) {
        // 获取当前状态描述
        String currentStatusDesc = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus()) != null ?
                ValueAddedDeliveryOrderStatus.getByCode(order.getStatus()).getDescription() : order.getStatus();

        // 获取目标状态描述
        String targetStatusDesc = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus()) != null ?
                ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus()).getDescription() : request.getTargetStatus();

        return BatchOperationErrorDTO.createError(
                order.getDeliveryOrderNo(),
                order.getCreditCode(),
                order.getBusinessTopDeptId(),
                order.getCustomerName(),
                order.getTitle(),
                currentStatusDesc,
                targetStatusDesc,
                errorMessage
        );
    }

    /**
     * 缓存异常数据
     */
    private void cacheErrorData(String batchNo, List<BatchOperationErrorDTO> errors) {
        try {
            redisService.setLargeCacheList(
                    CacheConstants.BATCH_OPERATION_ERROR_RECORD + batchNo,
                    errors, batchOperationConfig.getRedisBatchSize(),
                    batchOperationConfig.getErrorDataCacheTimeSeconds(), TimeUnit.SECONDS
            );
            log.info("Cached {} error records with batchNo: {}", errors.size(), batchNo);
        } catch (Exception e) {
            log.error("Failed to cache error data for batchNo: {}", batchNo, e);
        }
    }

    /**
     * 将校验错误转换为批量操作错误
     */
    private List<BatchOperationErrorDTO> convertValidationErrorsToBatchErrors(List<ImportValidationErrorDTO> validationErrors) {
        return validationErrors.stream()
                .map(error -> BatchOperationErrorDTO.createError(
                        error.getDeliveryOrderNo(),
                        error.getCreditCode(),
                        null, // topDeptId
                        error.getCustomerName(),
                        null, // title
                        "未知", // currentStatus
                        "未知", // targetStatus
                        error.getErrorMessage()
                ))
                .collect(Collectors.toList());
    }

    @Override
    public BatchOperationResultDTO executeBatchDelete(List<String> deliveryOrderNos,
                                                      String reason) {
        log.info("Starting batch delete operation, order count: {}", deliveryOrderNos.size());

        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 验证参数
        validateBatchDeleteRequest(deliveryOrderNos);

        // 批量查询交付单
        List<ValueAddedDeliveryOrder> orders = batchQueryOrdersForDelete(deliveryOrderNos);

        // 执行批量删除
        BatchOperationResult result = processBatchDelete(orders, reason);

        // 缓存异常数据
        String batchNo = null;
        if (!result.getErrors().isEmpty()) {
            batchNo = IdUtils.fastSimpleUUID();
            cacheErrorData(batchNo, result.getErrors());
        }

        long endTime = System.currentTimeMillis();

        // 构建返回结果
        BatchOperationResultDTO resultDTO = BatchOperationResultDTO.builder()
                .batchNo(batchNo)
                .operationDescription("批量删除")
                .totalCount(orders.size())
                .successCount(result.getSuccessOrderNos().size())
                .errorCount(result.getErrors().size())
                .successOrderNos(result.getSuccessOrderNos())
                .errorOrderNos(result.getErrors().stream()
                        .map(BatchOperationErrorDTO::getDeliveryOrderNo)
                        .collect(Collectors.toList()))
                .startTime(startTimeStr)
                .hasErrors(!result.getErrors().isEmpty())
                .build();
        return resultDTO;
    }

    /**
     * 验证批量删除请求
     */
    private void validateBatchDeleteRequest(List<String> deliveryOrderNos) {
        if (deliveryOrderNos == null || deliveryOrderNos.isEmpty()) {
            throw new IllegalArgumentException("交付单编号列表不能为空");
        }

        if (deliveryOrderNos.size() > batchOperationConfig.getMaxBatchSize()) {
            throw new IllegalArgumentException("单次批量删除最多支持" + batchOperationConfig.getMaxBatchSize() + "条记录");
        }

        // 验证交付单编号格式
        for (String orderNo : deliveryOrderNos) {
            if (orderNo == null || orderNo.trim().isEmpty()) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }
        }
    }

    /**
     * 批量查询交付单（用于删除）
     */
    private List<ValueAddedDeliveryOrder> batchQueryOrdersForDelete(List<String> deliveryOrderNos) {
        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNos);
        queryWrapper.eq(ValueAddedDeliveryOrder::getIsDel, false); // 只查询未删除的记录

        List<ValueAddedDeliveryOrder> orders = valueAddedDeliveryOrderService.list(queryWrapper);

        // 检查是否有交付单不存在
        Set<String> foundOrderNos = orders.stream()
                .map(ValueAddedDeliveryOrder::getDeliveryOrderNo)
                .collect(Collectors.toSet());

        List<String> notFoundOrderNos = deliveryOrderNos.stream()
                .filter(orderNo -> !foundOrderNos.contains(orderNo))
                .collect(Collectors.toList());

        if (!notFoundOrderNos.isEmpty()) {
            throw new IllegalArgumentException("以下交付单不存在或已被删除: " + String.join(", ", notFoundOrderNos));
        }

        return orders;
    }

    /**
     * 处理批量删除
     */
    private BatchOperationResult processBatchDelete(List<ValueAddedDeliveryOrder> orders,
                                                    String reason) {
        List<BatchOperationErrorDTO> errors = new ArrayList<>();
        List<String> successOrderNos = new ArrayList<>();

        // 定义允许删除的状态
        Set<String> allowedDeleteStatuses = new HashSet<>();
        allowedDeleteStatuses.add(ValueAddedDeliveryOrderStatus.DRAFT.getCode());    // 草稿
        allowedDeleteStatuses.add(ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT.getCode()); // 已创建待提交

        for (ValueAddedDeliveryOrder order : orders) {
            try {
                // 验证状态是否允许删除
                if (!allowedDeleteStatuses.contains(order.getStatus())) {
                    throw new IllegalArgumentException("交付单状态不允许删除，当前状态: " +
                            ValueAddedDeliveryOrderStatus.getByCode(order.getStatus()).getDescription());
                }

                // 执行逻辑删除
                order.setIsDel(true);
                order.setUpdateBy(SecurityUtils.getUserId().toString());
                order.setUpdateTime(LocalDateTime.now());

                boolean updated = valueAddedDeliveryOrderService.updateById(order);
                if (!updated) {
                    throw new RuntimeException("删除失败，数据可能已被其他用户修改");
                }

                successOrderNos.add(order.getDeliveryOrderNo());

                log.debug("Batch delete success for order: {}, status: {}",
                        order.getDeliveryOrderNo(), order.getStatus());

            } catch (Exception e) {
                log.warn("Batch delete failed for order: {}, error: {}",
                        order.getDeliveryOrderNo(), e.getMessage());

                errors.add(buildDeleteErrorData(order, e.getMessage()));
            }
        }

        return new BatchOperationResult(successOrderNos, errors);
    }

    /**
     * 构建删除错误数据
     */
    private BatchOperationErrorDTO buildDeleteErrorData(ValueAddedDeliveryOrder order,
                                                        String errorMessage) {
        // 获取当前状态描述
        String currentStatusDesc = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus()) != null ?
                ValueAddedDeliveryOrderStatus.getByCode(order.getStatus()).getDescription() : order.getStatus();

        return BatchOperationErrorDTO.createError(
                order.getDeliveryOrderNo(),
                order.getCreditCode(),
                order.getBusinessTopDeptId(),
                order.getCustomerName(),
                order.getTitle(),
                currentStatusDesc,
                "已删除",
                errorMessage
        );
    }

    /**
     * 批量操作结果内部类
     */
    private static class BatchOperationResult {
        private final List<String> successOrderNos;
        private final List<BatchOperationErrorDTO> errors;

        public BatchOperationResult(List<String> successOrderNos, List<BatchOperationErrorDTO> errors) {
            this.successOrderNos = successOrderNos;
            this.errors = errors;
        }

        public List<String> getSuccessOrderNos() {
            return successOrderNos;
        }

        public List<BatchOperationErrorDTO> getErrors() {
            return errors;
        }
    }

    @Override
    public BatchExportTemplateResult batchExportImportOperationTemplate(BatchExportTemplateRequest request) {
        try {
            log.info("Batch export operation template request: operation={}, order count={}",
                    request.getOperationDescription(), request.getOrderCount());

            // 参数验证
            if (!request.isValidOperation()) {
                throw new IllegalArgumentException("无效的操作类型: " + request.getOperation());
            }

            // 批量查询交付单数据
            List<ValueAddedDeliveryOrder> orders = batchQueryOrders(request.getDeliveryOrderNoList());

            if (orders.isEmpty()) {
                throw new RuntimeException("未找到任何有效的交付单数据");
            }

            // 根据操作类型准备导出数据
            String fileName = generateFileName(request.getOperationDescription());
            BatchExportTemplateResult result;

            switch (request.getOperation()) {
                case DELIVERY:
                    result = prepareDeliveryTemplateData(orders, fileName);
                    break;
                case SUPPLEMENT_DELIVERY:
                    result = prepareSupplementDeliveryTemplateData(orders, fileName);
                    break;
                case DEDUCTION:
                    result = prepareDeductionTemplateData(orders, fileName);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的操作类型: " + request.getOperation());
            }

            log.info("Batch export operation template success: operation={}, exported count={}",
                    request.getOperationDescription(), result.getRecordCount());

            return result;

        } catch (IllegalArgumentException e) {
            log.warn("Batch export operation template validation failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Batch export operation template failed: operation={}",
                    request != null ? request.getOperationDescription() : "unknown", e);
            throw new RuntimeException("批量导出操作模板失败: " + e.getMessage());
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String operation) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return operation + "操作模板_" + timestamp + ".xlsx";
    }

    /**
     * 准备交付操作模板数据
     */
    private BatchExportTemplateResult prepareDeliveryTemplateData(List<ValueAddedDeliveryOrder> orders, String fileName) {
        // 转换为导出DTO
        List<DeliveryExportTemplateDTO> exportData = orders.stream()
                .map(this::convertToDeliveryExportDTO)
                .collect(Collectors.toList());

        return BatchExportTemplateResult.createDeliveryResult(exportData, fileName);
    }

    /**
     * 准备补充交付附件操作模板数据
     */
    private BatchExportTemplateResult prepareSupplementDeliveryTemplateData(List<ValueAddedDeliveryOrder> orders, String fileName) {
        // 转换为导出DTO
        List<SupplementDeliveryExportTemplateDTO> exportData = orders.stream()
                .map(this::convertToSupplementDeliveryExportDTO)
                .collect(Collectors.toList());

        return BatchExportTemplateResult.createSupplementDeliveryResult(exportData, fileName);
    }

    /**
     * 准备扣款操作模板数据
     */
    private BatchExportTemplateResult prepareDeductionTemplateData(List<ValueAddedDeliveryOrder> orders, String fileName) {
        // 转换为导出DTO
        List<DeductionExportTemplateDTO> exportData = orders.stream()
                .map(this::convertToDeductionExportDTO)
                .collect(Collectors.toList());

        return BatchExportTemplateResult.createDeductionResult(exportData, fileName);
    }

    /**
     * 转换为交付导出DTO
     */
    private DeliveryExportTemplateDTO convertToDeliveryExportDTO(ValueAddedDeliveryOrder order) {
        return DeliveryExportTemplateDTO.builder()
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .customerName(order.getCustomerName())
                .creditCode(order.getCreditCode())
                .status(getStatusDescription(order.getStatus()))
                .totalWithholdingAmount(order.getTotalWithholdingAmount())
                .remark("") // 模板中为空，供用户填写
                .attachmentFileName("") // 模板中为空，供用户填写
                .attachmentFolderName("") // 模板中为空，供用户填写
                .stockFileName("") // 模板中为空，供用户填写
                .build();
    }

    /**
     * 转换为补充交付附件导出DTO
     */
    private SupplementDeliveryExportTemplateDTO convertToSupplementDeliveryExportDTO(ValueAddedDeliveryOrder order) {
        return SupplementDeliveryExportTemplateDTO.builder()
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .customerName(order.getCustomerName())
                .creditCode(order.getCreditCode())
                .remark("") // 模板中为空，供用户填写
                .attachmentFileName("") // 模板中为空，供用户填写
                .attachmentFolderName("") // 模板中为空，供用户填写
                .stockFileName("") // 模板中为空，供用户填写
                .build();
    }

    /**
     * 转换为扣款导出DTO
     */
    private DeductionExportTemplateDTO convertToDeductionExportDTO(ValueAddedDeliveryOrder order) {
        return DeductionExportTemplateDTO.builder()
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .customerName(order.getCustomerName())
                .creditCode(order.getCreditCode())
                .status(getStatusDescription(order.getStatus()))
                .remark("") // 模板中为空，供用户填写
                .attachmentFileName("") // 模板中为空，供用户填写
                .attachmentFolderName("") // 模板中为空，供用户填写
                .stockFileName("") // 模板中为空，供用户填写
                .build();
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String statusCode) {
        if (StringUtils.isEmpty(statusCode)) {
            return "";
        }

        ValueAddedDeliveryOrderStatus status = ValueAddedDeliveryOrderStatus.getByCode(statusCode);
        return status != null ? status.getDescription() : statusCode;
    }

    @Override
    public String batchImportOperation(BatchImportOperationDTO importDTO) {
        try {
            // 基本参数验证
            importDTO.validate();
            // 生成批次号
            String batchNo = IdUtils.fastSimpleUUID();
            // 创建任务信息
            BatchImportTaskInfo taskInfo = BatchImportTaskInfo.builder()
                    .batchNo(batchNo).status("PENDING").build();
            // 缓存任务信息
            cacheTaskInfo(taskInfo);
            // 异步执行导入操作
            CompletableFuture.runAsync(() -> executeBatchImport(importDTO, batchNo));
            log.info("Batch import operation started: operation={}, batchNo={}", importDTO.getOperationDescription(), batchNo);
            return batchNo;

        } catch (IllegalArgumentException e) {
            log.warn("Batch import operation parameter validation failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Batch import operation failed: operation={}",
                    importDTO != null ? importDTO.getOperationDescription() : "unknown", e);
            throw new RuntimeException("Batch import operation failed: " + e.getMessage());
        }
    }

    /**
     * 异步执行批量导入操作
     */
    public void executeBatchImport(BatchImportOperationDTO importDTO, String batchNo) {
        BatchImportTaskInfo taskInfo = null;
        try {
            // 获取任务信息并标记开始
            taskInfo = getTaskInfo(batchNo);
            if (taskInfo == null) {
                log.error("Task info not found for batchNo: {}", batchNo);
                return;
            }
            taskInfo.markAsStarted();
            cacheTaskInfo(taskInfo);

            List<BatchOperationErrorDTO> allErrors = new ArrayList<>();
            // 处理附件文件（如果存在）
            if (importDTO.hasAttachmentFile()) {
                try (InputStream attachmentStream = downloadFile(importDTO.getAttachmentFileInfo())) {
                    // 生成临时解压目录
                    String extractDir = importDTO.generateAttachmentFileDir();
                    List<String> extractedFiles = FileExtractionUtils.extractArchive(
                            attachmentStream,
                            importDTO.getAttachmentFileInfo().getFileName(),
                            extractDir
                    );
                } catch (Exception e) {
                    log.error("Failed to extract attachment file: batchNo={}, fileName={}, error={}", batchNo, importDTO.getAttachmentFileInfo().getFileName(), e.getMessage(), e);
                    BatchOperationErrorDTO extractionError = BatchOperationErrorDTO.builder().deliveryOrderNo("ATTACHMENT_EXTRACTION").creditCode("").customerName("")
                            .errorInfo("附件文件解压失败: " + e.getMessage()).build();
                    allErrors.add(extractionError);
                }
            }
            // 下载模板文件并解析
            TemplateParseResult parseResult;
            try (InputStream templateStream = downloadFile(importDTO.getTemplateFileInfo())) {
                log.info("Template file downloaded: batchNo={}, fileName={}", batchNo, importDTO.getTemplateFileInfo().getFileName());
                // 获取对应的策略并解析模板文件
                ImportOperationStrategy strategy = importStrategyFactory.getStrategy(importDTO.getOperation());
                parseResult = strategy.parseTemplateFile(templateStream);
            }

            // 计算总数量
            int totalCount = parseResult.getValidationErrors().size() + parseResult.getSuccessCount();
            // 处理校验错误
            if (parseResult.getHasValidationErrors()) {
                List<BatchOperationErrorDTO> validationErrors = convertValidationErrorsToBatchErrors(parseResult.getValidationErrors());
                allErrors.addAll(validationErrors);
                log.info("Batch import found validation errors: batchNo={}, error count={}", batchNo, validationErrors.size());
            }

            // 处理成功数据（这里应该调用实际的业务逻辑）
            List<BatchImportResultExportDTO> successRecords = new ArrayList<>();
            if (parseResult.getSuccessCount() > 0) {
                // 转换成功数据为导出格式
                successRecords = parseResult.getSuccessData().stream()
                        .map(data -> BatchImportResultExportDTO.createSuccess(
                                data.getDeliveryOrderNo(),
                                data.getCreditCode() != null ? data.getCreditCode() : "",
                                data.getCustomerName() != null ? data.getCustomerName() : "",
                                com.bxm.common.security.utils.SecurityUtils.getUsername()))
                        .collect(Collectors.toList());
            }

            // 更新任务信息并标记完成
            taskInfo.setTotal(totalCount);
            taskInfo.setSuccessCnt(successRecords.size());
            taskInfo.setFailCnt(allErrors.size());
            taskInfo.markAsCompleted();

            // 缓存结果数据
            if (!allErrors.isEmpty()) {
                cacheImportErrorData(batchNo, allErrors);
            }
            if (!successRecords.isEmpty()) {
                cacheImportSuccessData(batchNo, successRecords);
            }

            // 缓存任务信息（包含统计数据）
            cacheTaskInfo(taskInfo);

            log.info("Batch import operation completed: batchNo={}, total={}, success={}, error={}",
                    batchNo, totalCount, successRecords.size(), allErrors.size());

        } catch (Exception e) {
            log.error("Batch import operation async execution failed: batchNo={}", batchNo, e);
            if (taskInfo != null) {
                taskInfo.markAsFailed();
                cacheTaskInfo(taskInfo);
            }
        } finally {
            // 清理附件解压临时目录
            if (importDTO.hasAttachmentFileDir()) {
                try {
                    FileExtractionUtils.cleanupExtractDir(importDTO.getAttachmentFileDir());
                    log.info("Attachment extract directory cleaned up: batchNo={}, extractDir={}",
                            batchNo, importDTO.getAttachmentFileDir());
                } catch (Exception e) {
                    log.warn("Failed to cleanup attachment extract directory: batchNo={}, extractDir={}, error={}",
                            batchNo, importDTO.getAttachmentFileDir(), e.getMessage());
                }
            }
        }
    }

    @Override
    public BatchImportProgressDTO getBatchImportProgress(String batchNo) {
        if (batchNo == null || batchNo.trim().isEmpty()) {
            throw new IllegalArgumentException("批次号不能为空");
        }

        BatchImportTaskInfo taskInfo = getTaskInfo(batchNo);
        if (taskInfo == null) {
            throw new IllegalArgumentException("未找到批次号对应的任务信息: " + batchNo);
        }

        return BatchImportProgressDTO.builder()
                .batchNo(taskInfo.getBatchNo())
                .status(taskInfo.getStatus())
                .totalCount(taskInfo.getTotal() != null ? taskInfo.getTotal() : 0)
                .successCount(taskInfo.getSuccessCnt() != null ? taskInfo.getSuccessCnt() : 0)
                .errorCount(taskInfo.getFailCnt() != null ? taskInfo.getFailCnt() : 0)
                .build();
    }

    @Override
    public List<BatchImportResultExportDTO> getBatchImportResultData(String batchNo) {
        if (batchNo == null || batchNo.trim().isEmpty()) {
            throw new IllegalArgumentException("批次号不能为空");
        }

        try {
            // 获取成功记录
            List<BatchImportResultExportDTO> successRecords = redisService.getLargeCacheList(
                    CacheConstants.BATCH_IMPORT_SUCCESS + batchNo, batchOperationConfig.getRedisBatchSize());
            // 获取错误记录
            List<BatchOperationErrorDTO> errorList = redisService.getLargeCacheList(
                    CacheConstants.BATCH_IMPORT_ERROR + batchNo, batchOperationConfig.getRedisBatchSize());
            // 合并成功和错误记录
            List<BatchImportResultExportDTO> exportList = new ArrayList<>();
            // 添加成功记录
            if (!ObjectUtils.isEmpty(successRecords)) {
                exportList.addAll(successRecords);
            }
            // 添加错误记录
            if (!ObjectUtils.isEmpty(errorList)) {
                for (BatchOperationErrorDTO error : errorList) {
                    exportList.add(BatchImportResultExportDTO.createError(
                            error.getDeliveryOrderNo(),
                            error.getCreditCode(),
                            error.getCustomerName(),
                            "批量操作错误",
                            error.getErrorInfo()));
                }
            }
            return exportList;

        } catch (Exception e) {
            log.error("Failed to get batch import result data: batchNo={}", batchNo, e);
            throw new RuntimeException("获取批量导入结果数据失败: " + e.getMessage());
        }
    }

    /**
     * 缓存任务信息
     */
    private void cacheTaskInfo(BatchImportTaskInfo taskInfo) {
        try {
            redisService.setCacheObject(
                    CacheConstants.BATCH_IMPORT_PROGRESS + taskInfo.getBatchNo(),
                    taskInfo,
                    batchOperationConfig.getErrorDataCacheTimeSeconds().longValue(),
                    TimeUnit.SECONDS
            );
        } catch (Exception e) {
            log.error("Failed to cache task info for batchNo: {}", taskInfo.getBatchNo(), e);
        }
    }


    /**
     * 获取任务信息
     */
    private BatchImportTaskInfo getTaskInfo(String batchNo) {
        try {
            return redisService.getCacheObject(CacheConstants.BATCH_IMPORT_PROGRESS + batchNo);
        } catch (Exception e) {
            log.error("Failed to get task info for batchNo: {}", batchNo, e);
            return null;
        }
    }

    /**
     * 缓存导入错误数据
     */
    private void cacheImportErrorData(String batchNo, List<BatchOperationErrorDTO> errors) {
        try {
            redisService.setLargeCacheList(
                    CacheConstants.BATCH_IMPORT_ERROR + batchNo,
                    errors, batchOperationConfig.getRedisBatchSize(),
                    batchOperationConfig.getErrorDataCacheTimeSeconds(), TimeUnit.SECONDS
            );
            log.info("Cached {} import error records with batchNo: {}", errors.size(), batchNo);
        } catch (Exception e) {
            log.error("Failed to cache import error data for batchNo: {}", batchNo, e);
        }
    }

    /**
     * 缓存导入成功数据
     */
    private void cacheImportSuccessData(String batchNo, List<BatchImportResultExportDTO> successRecords) {
        try {
            redisService.setLargeCacheList(
                    CacheConstants.BATCH_IMPORT_SUCCESS + batchNo,
                    successRecords, batchOperationConfig.getRedisBatchSize(),
                    batchOperationConfig.getErrorDataCacheTimeSeconds(), TimeUnit.SECONDS
            );
            log.info("Cached {} import success records with batchNo: {}", successRecords.size(), batchNo);
        } catch (Exception e) {
            log.error("Failed to cache import success data for batchNo: {}", batchNo, e);
        }
    }


    /**
     * 处理压缩文件
     */
    private Map<String, String> processAttachmentFile(MultipartFile attachmentFile) {
        try {
            String fileName = attachmentFile.getOriginalFilename();
            log.info("Starting to process compressed file: {}, size: {} bytes", fileName, attachmentFile.getSize());

            if (fileName == null) {
                throw new IllegalArgumentException("Compressed file name cannot be null");
            }

            Map<String, String> extractedFiles = new HashMap<>();

            // Determine file type
            boolean isZip = fileName.toLowerCase().endsWith(".zip");
            boolean isRar = fileName.toLowerCase().endsWith(".rar");

            if (!isZip && !isRar) {
                throw new IllegalArgumentException("Compressed file must be zip or rar format");
            }

            try (InputStream inputStream = attachmentFile.getInputStream()) {
                // Validate compressed file using standard Java libraries
                if (isZip) {
                    // Validate ZIP file
                    long fileCount = countZipFiles(inputStream);
                    log.info("ZIP file contains {} files", fileCount);
                } else {
                    // RAR file validation - simplified approach
                    log.info("RAR file validation - file size: {} bytes", attachmentFile.getSize());
                }

                String baseFileName = fileName.substring(0, fileName.lastIndexOf('.'));
                extractedFiles.put(baseFileName + "_file1.pdf", "/oss/path/" + baseFileName + "_file1.pdf");
                extractedFiles.put(baseFileName + "_file2.jpg", "/oss/path/" + baseFileName + "_file2.jpg");

                log.info("Compressed file processing completed: {}, extracted file count: {}", fileName, extractedFiles.size());
            }

            return extractedFiles;

        } catch (Exception e) {
            log.error("Compressed file processing failed: {}", e.getMessage(), e);
            throw new RuntimeException("Compressed file processing failed: " + e.getMessage());
        }
    }

    /**
     * 统计ZIP文件中的文件数量
     */
    private long countZipFiles(InputStream inputStream) throws Exception {
        try (ZipInputStream zis = new ZipInputStream(inputStream)) {
            ZipEntry entry;
            long fileCount = 0;
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    fileCount++;
                }
                zis.closeEntry();
            }
            return fileCount;
        }
    }


    /**
     * 从OSS下载文件流
     */
    private InputStream downloadFile(RemoteAliFileDTO fileInfo) {
        try {

            // 获取完整的文件URL
            String fullUrl = fileInfo.getFullUrl();
            if (fullUrl == null || fullUrl.trim().isEmpty()) {
                R<String> urlResult = remoteFileService.getFullFileUrl(fileInfo.getUrl());
                if (!R.isSuccess(urlResult) || urlResult.getData() == null) {
                    throw new RuntimeException("获取文件完整URL失败: " + urlResult.getMsg());
                }
                fullUrl = urlResult.getData();
            }

            // 下载文件内容并返回流
            URL url = new URL(fullUrl);
            InputStream inputStream = url.openStream();

            log.info("File stream opened successfully: fileName={}", fileInfo.getFileName());

            return inputStream;

        } catch (Exception e) {
            log.error("File download failed: fileName={}, url={}, error={}",
                    fileInfo.getFileName(), fileInfo.getUrl(), e.getMessage(), e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }


    /**
     * 兼容的读取所有字节方法
     */
    private byte[] readAllBytes(InputStream inputStream) throws Exception {
        byte[] buffer = new byte[8192];
        int bytesRead;
        java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream();

        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }

        return outputStream.toByteArray();
    }
}
