package com.bxm.customer.service.strategy.valueadded;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.helper.ValueAddedEmpValidationHelper;
import com.bxm.customer.service.strategy.AbstractEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 个税明细业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class PersonalTaxUpsertStrategy extends AbstractEmployeeUpsertStrategy {

    @Autowired
    private ValueAddedEmpValidationHelper validationHelper;

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.PERSONAL_TAX.getCode();
    }

    @Override
    protected void validateBusinessFields(ValueAddedEmployeeVO employeeVO) {
        // 减员操作跳过业务规则校验，只需要基本信息即可
        if (ValueAddedEmpValidationHelper.isReductionOperation(employeeVO.getOperationType())) {
            // 减员操作不需要校验手机号、申报基数、申报险种、应发工资等业务规则
            return;
        }
        // 2. 校验姓名（必填，2-50个字，至少填2个字）
        ValueAddedEmpValidationHelper.validateEmployeeName(employeeVO.getEmployeeName());

        // 3. 校验身份证号（必填，18个数字）
        ValueAddedEmpValidationHelper.validateIdNumberStrict(employeeVO.getIdNumber());



        // 4. 校验手机号（必填，11个数字）- 仅增员和更正需要
        ValueAddedEmpValidationHelper.validateMobile(employeeVO.getMobile());

        // 5. 校验同一交付单内的唯一性（身份证号和手机号）- 仅增员和更正需要
        validationHelper.validateUniqueness(employeeVO, ValueAddedBizType.PERSONAL_TAX.getCode());

        // 6. 校验申报基数（方式为增员或更正时必填，浮点2位数字）
        ValueAddedEmpValidationHelper.validateSocialInsuranceBase(
                employeeVO.getSocialInsuranceBase(), employeeVO.getOperationType());

        // 7. 校验申报险种前置条件：方式为增员或更正时必填，至少选中一个
        if (ValueAddedEmpValidationHelper.isAddOrCorrectionOperation(employeeVO.getOperationType())) {
            if (employeeVO.getSocialInsurance() == null) {
                throw new IllegalArgumentException("方式为增员或更正时，申报险种不能为空");
            }

            // 检查是否至少选中一个险种
            if (!employeeVO.getSocialInsurance().isValid()) {
                String validationMessage = employeeVO.getSocialInsurance().getValidationMessage();
                if (validationMessage != null) {
                    throw new IllegalArgumentException(validationMessage);
                } else {
                    throw new IllegalArgumentException("方式为增员或更正时，申报险种至少需要选中一个");
                }
            }
        }

        // 8. 校验备注（当选择申报险种为"其他"时，备注必填）
        ValueAddedEmpValidationHelper.validateRemark(employeeVO.getRemark(), employeeVO.getSocialInsurance());

        // 9. 验证应发工资（个税明细业务必须有工资信息）
        if (employeeVO.getGrossSalary() == null || employeeVO.getGrossSalary().doubleValue() <= 0) {
            throw new IllegalArgumentException("个税明细业务应发工资不能为空且必须为正数");
        }
    }

    @Override
    protected void preprocessEmployee(ValueAddedEmployee employee) {
        // 标准化身份证号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getIdNumber())) {
            employee.setIdNumber(employee.getIdNumber().trim().toUpperCase());
        }
    }

    @Override
    protected ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 个税明细业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        return findExistingEmployeeByIdNumber(employee, ValueAddedBizType.PERSONAL_TAX.getCode());
    }

    @Override
    protected ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 先调用父类的通用合并逻辑
        super.mergeEmployee(existing, newEmployee);

        // 个税明细特定字段的非空值复制
        if (newEmployee.getGrossSalary() != null) {
            existing.setGrossSalary(newEmployee.getGrossSalary());
        }
        if (newEmployee.getProvidentFundPersonal() != null) {
            existing.setProvidentFundPersonal(newEmployee.getProvidentFundPersonal());
        }
        if (newEmployee.getSocialInsurance() != null) {
            existing.setSocialInsurance(newEmployee.getSocialInsurance());
        }

        return existing;
    }
}
