package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceTaxTypeCheckVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("税种名称")
    private String taxTypeName;

    @ApiModelProperty("上报类型，1-月报，2-季报，3-年报，4-次报，5-半年报，6-无需申报")
    private Integer reportType;

    private Long customerServiceId;
}
