package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 客户服务系统账号对象 c_customer_sys_account
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Data
@ApiModel("客户服务系统账号对象")
@Accessors(chain = true)
@TableName("c_customer_sys_account")
public class CustomerSysAccount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    /** 系统账号类型，1-医保，2-社保，3-公积金，4-个税（工资薪金），5-国税 */
    @Excel(name = "系统账号类型，1-医保，2-社保，3-公积金，4-个税（工资薪金），5-国税")
    @TableField("sys_type")
    @ApiModelProperty(value = "系统账号类型，1-医保，2-社保，3-公积金，4-个税（工资薪金），5-国税")
    private Integer sysType;

    /** 系统账号类型，1-医保，2-社保，3-公积金，4-个税（工资薪金），5-国税 */
    @Excel(name = "系统账号类型名称")
    @TableField("sys_type_name")
    @ApiModelProperty(value = "系统账号类型名称")
    private String sysTypeName;

    /** 账号 */
    @Excel(name = "账号")
    @TableField("account")
    @ApiModelProperty(value = "账号")
    private String account;

    /** 密码 */
    @Excel(name = "密码")
    @TableField("password")
    @ApiModelProperty(value = "密码")
    private String password;

    /** 登陆类型 */
    @Excel(name = "登陆类型")
    @TableField("login_type")
    @ApiModelProperty(value = "登陆类型")
    private String loginType;

    /** 实名人员 */
    @Excel(name = "实名人员")
    @TableField("contact")
    @ApiModelProperty(value = "实名人员")
    private String contact;

    /** 实名手机号 */
    @Excel(name = "实名手机号")
    @TableField("id_number")
    @ApiModelProperty(value = "实名手机号")
    private String contactMobile;

    @Excel(name = "身份证号")
    @TableField("contact_mobile")
    @ApiModelProperty("身份证号")
    private String idNumber;

    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty("备注")
    private String remark;

    /** 账号是否同信用代码，0-否，1-是 */
    @Excel(name = "账号是否同信用代码，0-否，1-是")
    @TableField("is_same_with_credit_code")
    @ApiModelProperty(value = "账号是否同信用代码，0-否，1-是")
    private Boolean isSameWithCreditCode;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

}
