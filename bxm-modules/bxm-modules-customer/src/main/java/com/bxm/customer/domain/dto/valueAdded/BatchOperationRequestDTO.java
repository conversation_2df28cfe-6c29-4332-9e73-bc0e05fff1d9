package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.customer.domain.dto.BaseStatusChangeRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * 批量操作请求DTO
 *
 * 用于增值交付单批量操作的请求参数封装
 * 支持多种批量操作类型，包括批量确认、提交、关闭、处理异常、驳回、退回等
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量操作请求DTO")
public class BatchOperationRequestDTO extends BaseStatusChangeRequestDTO {

    /**
     * 交付单编号列表
     */
    @NotEmpty(message = "交付单编号列表不能为空")
    @Size(max = 1000, message = "单次批量操作最多支持1000条记录")
    @ApiModelProperty(value = "交付单编号列表", required = true, example = "[\"VAD2508051430001A1C\", \"VAD2508051430002A1C\"]")
    private List<String> deliveryOrderNos;

    /**
     * 前置状态（批量退回操作时必填，用于确定目标状态）
     */
    @ApiModelProperty(value = "前置状态，批量退回操作时必填", example = "DEDUCTION_COMPLETED")
    private String sourceStatus;

    /**
     * 操作原因
     */
    @Size(max = 500, message = "操作原因长度不能超过500个字符")
    @ApiModelProperty(value = "操作原因", example = "批量确认交付单")
    private String reason;

    /**
     * 操作类型名称到描述的映射
     */
    private static final Map<String, String> OPERATION_DESCRIPTIONS = new HashMap<>();

    static {
        OPERATION_DESCRIPTIONS.put("BATCH_CONFIRM", "批量确认");
        OPERATION_DESCRIPTIONS.put("BATCH_SUBMIT", "批量提交");
        OPERATION_DESCRIPTIONS.put("BATCH_CLOSE_DEDUCTION", "批量关闭扣款");
        OPERATION_DESCRIPTIONS.put("BATCH_CLOSE_DELIVERY", "批量关闭交付");
        OPERATION_DESCRIPTIONS.put("BATCH_RESOLVE_DEDUCTION_EXCEPTION", "批量解除扣款异常");
        OPERATION_DESCRIPTIONS.put("BATCH_RESOLVE_DELIVERY_EXCEPTION", "批量解除交付异常");
        OPERATION_DESCRIPTIONS.put("BATCH_REJECT", "批量驳回");
        OPERATION_DESCRIPTIONS.put("BATCH_RETURN", "批量退回");
        OPERATION_DESCRIPTIONS.put("BATCH_DELETE", "批量删除");
    }

    /**
     * 验证批量退回操作的前置状态
     */
    public void validateBatchReturnSourceStatus() {
        if ("BATCH_RETURN".equals(getOperTypeName())) {
            if (sourceStatus == null || sourceStatus.trim().isEmpty()) {
                throw new IllegalArgumentException("批量退回操作必须指定前置状态");
            }
        }
    }

    /**
     * 获取交付单数量
     */
    public int getOrderCount() {
        return deliveryOrderNos == null ? 0 : deliveryOrderNos.size();
    }
}
