package com.bxm.customer.domain.dto;

import com.bxm.file.api.domain.ValueAddedFileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 状态变更请求基类DTO
 *
 * 包含状态变更的公共字段，供单个和批量状态变更继承使用
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("状态变更请求基类DTO")
public class BaseStatusChangeRequestDTO {

    /**
     * 目标状态（必填）
     */
    @NotBlank(message = "目标状态不能为空")
    @Size(max = 50, message = "目标状态长度不能超过50个字符")
    @ApiModelProperty(value = "目标状态", required = true, example = "SAVED_PENDING_SUBMIT")
    protected String targetStatus;

    @ApiModelProperty(value = "处理结果", example = "仅处结果时操作日志用到")
    protected String dealResult;

    @ApiModelProperty(value = "交付结果", example = "仅处结果时操作日志用到")
    protected String deliverResult;

    @ApiModelProperty(value = "扣款结果", example = "仅处结果时操作日志用到")
    protected String deductionResult;

    /**
     * 操作备注
     */
    @Size(max = 1000, message = "操作备注长度不能超过1000个字符")
    @ApiModelProperty(value = "操作备注", example = "系统自动状态变更")
    protected String remark;

    /**
     * 交付备注
     */
    @Size(max = 500, message = "交付备注长度不能超过500个字符")
    @ApiModelProperty(value = "交付备注", example = "交付相关说明")
    protected String deliveryRemark;

    /** 统一社会信用代码 */
    //@NotBlank(message = "统一社会信用代码不能为空")
    @ApiModelProperty(value = "统一社会信用代码", required = true)
    protected String creditCode;

    /**
     * 总扣缴额（可选）
     */
    @DecimalMin(value = "0.00", message = "总扣缴额不能为负数")
    @ApiModelProperty(value = "总扣缴额", example = "1000.00", notes = "可选字段，用于扣款相关状态")
    protected BigDecimal totalWithholdingAmount;

    /** 交付材料列表，c_value_added_file fileType为1 */
    @ApiModelProperty(value = "交付材料对象列表")
    protected List<ValueAddedFileDTO> deliveryFiles;

    /** 标准附件列表，c_value_added_file fileType为5 */
    @ApiModelProperty(value = "标准附件对象列表")
    protected List<ValueAddedFileDTO> standardFiles;

    /** 库存表列表，c_value_added_file fileType为6 */
    @ApiModelProperty(value = "库存表对象列表")
    protected List<ValueAddedFileDTO> stockFiles;

    /** 操作附件列表 */
    @ApiModelProperty(value = "操作附件对象列表")
    protected List<ValueAddedFileDTO> operationAttachments;

    /** 交付附件列表 */
    @ApiModelProperty(value = "交付附件对象列表")
    protected List<ValueAddedFileDTO> deliveryAttachments;

    /** 标准附件列表 */
    @ApiModelProperty(value = "标准附件对象列表")
    protected List<ValueAddedFileDTO> standardAttachments;

    /**
     * 操作类型名称
     * 前端传递操作类型代码，如：check、reject、deliver等
     */
    @Size(max = 100, message = "操作类型名称长度不能超过100个字符")
    @ApiModelProperty(value = "操作类型名称", example = "check", notes = "前端传递操作类型代码，后端记录日志时转换为中文描述")
    protected String operTypeName;

    /** 入账方式，1-凭票入账，2-全量入账 */
    @ApiModelProperty(value = "入账方式", notes = "1-凭票入账，2-全量入账", allowableValues = "1,2")
    protected Integer accountingMethod;

    protected Long deptId;
}
